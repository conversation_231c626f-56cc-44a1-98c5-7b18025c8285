cc.Class({
    extends: cc.Component,

    properties: {
        // ===== 基础配置 =====
        // 地面移动速度，将由GameManager控制
        moveSpeed: {
            default: 200,
            tooltip: "地面移动速度"
        },

        // 地面间距配置
        minGapX: {
            default: 50,
            tooltip: "地面之间的最小间距"
        },

        maxGapX: {
            default: 200,
            tooltip: "地面之间的最大间距"
        },

        // ===== 预制体引用 =====
        // 安全地面预制体
        safePlatformPrefab: {
            default: null,
            type: cc.Prefab,
            tooltip: "安全地面预制体，保证玩家可以安全跳跃"
        },

        // 挑战地面预制体数组
        challengePlatformPrefabs: {
            default: [],
            type: cc.Prefab,
            tooltip: "挑战地面预制体数组"
        },

        // ===== 生成规则配置 =====
        // 安全地面生成频率（每隔几个平台强制生成一个安全平台）
        safeFrequency: {
            default: 3,
            tooltip: "每隔几个平台强制生成一个安全平台"
        },

        // 预生成距离，屏幕宽度的倍数
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        console.log("PlatformManager 初始化开始");

        // 获取屏幕宽度
        this.screenWidth = cc.winSize.width;

        // 初始化状态标记
        this.isUsingPrefabs = false; // 是否已经切换到预制体生成模式
        this.platformCounter = 0;    // 生成的平台计数器，用于控制安全平台频率

        // 初始化数组
        this.presetPlatforms = [];   // 预设地面数组（场景中的Ground节点）
        this.prefabPlatforms = [];   // 预制体地面数组（动态生成的）
        this.platformPool = [];      // 地面对象池，用于回收重用

        // 获取预设地面（场景中的Ground节点）
        this.initPresetPlatforms();

        // 验证预制体引用
        this.validatePrefabs();

        console.log("PlatformManager 初始化完成");
        console.log("预设地面数量:", this.presetPlatforms.length);
        console.log("挑战地面预制体数量:", this.challengePlatformPrefabs.length);
    },

    // 获取场景中的预设地面
    initPresetPlatforms() {
        // 获取所有以"Ground"开头的子节点
        this.presetPlatforms = this.node.children.filter(child =>
            child.name.startsWith('Ground'));

        console.log("找到预设地面:", this.presetPlatforms.map(p => p.name));

        // 为每个预设地面添加标记
        this.presetPlatforms.forEach(platform => {
            platform._isPreset = true; // 标记为预设地面
            platform._isActive = true; // 标记为活跃状态
        });
    },

    // 验证预制体引用是否正确
    validatePrefabs() {
        let hasError = false;

        if (!this.safePlatformPrefab) {
            console.error("错误：未设置安全地面预制体！");
            hasError = true;
        }

        if (this.challengePlatformPrefabs.length === 0) {
            console.error("错误：未设置挑战地面预制体！");
            hasError = true;
        }

        if (hasError) {
            console.error("请在编辑器中为PlatformManager组件设置预制体引用！");
        } else {
            console.log("预制体验证通过");
        }
    },

    start() {
        // 确保所有预设地面都有物理组件
        this.setupPhysicsForPresetPlatforms();
    },

    // 为预设地面设置物理组件
    setupPhysicsForPresetPlatforms() {
        this.presetPlatforms.forEach(platform => {
            this.setupPlatformPhysics(platform);
        });
    },

    // 为单个地面设置物理组件
    setupPlatformPhysics(platform) {
        // 添加刚体组件
        if (!platform.getComponent(cc.RigidBody)) {
            let rigidBody = platform.addComponent(cc.RigidBody);
            rigidBody.type = cc.RigidBodyType.Static;
            rigidBody.gravityScale = 0;
        }

        // 添加碰撞器组件
        if (!platform.getComponent(cc.PhysicsBoxCollider)) {
            let collider = platform.addComponent(cc.PhysicsBoxCollider);
            collider.size = platform.getContentSize();
            collider.sensor = false;
            collider.friction = 0.2;
            collider.restitution = 0;
        }
    },

    update(dt) {
        // 移动所有活跃的地面
        this.moveAllPlatforms(dt);

        // 检查是否需要生成新地面
        this.checkAndGeneratePlatforms();

        // 回收移出屏幕的地面
        this.recyclePlatforms();
    },

    // 移动所有地面
    moveAllPlatforms(dt) {
        // 移动预设地面
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });

        // 移动预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });
    },

    // 检查并生成新地面
    checkAndGeneratePlatforms() {
        // TODO: 实现生成逻辑
        // 这个方法将在后续任务中完善
    },

    // 回收移出屏幕的地面
    recyclePlatforms() {
        // TODO: 实现回收逻辑
        // 这个方法将在后续任务中完善
    },

    // ===== 公共接口方法 =====

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    },

    // 重置所有平台位置
    resetPlatforms() {
        // TODO: 实现重置逻辑
        // 这个方法将在后续任务中完善
    }
});