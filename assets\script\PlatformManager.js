cc.Class({
    extends: cc.Component,

    properties: {
        // 地面移动速度，将由GameManager控制
        moveSpeed: 200,
        
        // 地面重新放置时的最小间距
        minGapX: 50,
        
        // 地面重新放置时的最大间距
        maxGapX: 200,
        
        // 地面的Y轴可能位置
        possibleHeights: {
            default: [],
            type: cc.Float,
            tooltip: "地面可能的高度位置"
        },
        
        // 预生成距离，屏幕宽度的倍数
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        // 获取屏幕宽度
        this.screenWidth = cc.winSize.width;
        
        // 获取所有地面节点
        this.platforms = this.node.children.filter(child => 
            child.name.startsWith('Ground'));
            
        // 如果没有指定高度，使用当前高度
        if (this.possibleHeights.length === 0) {
            // 使用当前所有地面的高度作为可能的高度
            this.platforms.forEach(platform => {
                let height = platform.position.y;
                if (!this.possibleHeights.includes(height)) {
                    this.possibleHeights.push(height);
                }
            });
        }
        
        // 初始化每个地面的数据
        this.platformsData = [];
        this.platforms.forEach(platform => {
            this.platformsData.push({
                node: platform,
                width: platform.width
            });
        });
        
        // 初始化地面位置，确保游戏开始时有足够的地面
        this.initializePositions();
    },
    
    initializePositions() {
        // 先清理现有位置，重新排列所有地面
        let startX = -this.screenWidth / 2;
        let lastRightEdge = startX;
        
        // 确保地面覆盖从左边到右边预生成距离的范围
        let targetRightEdge = this.screenWidth * this.preGenerateDistance;
        
        // 确保第一个平台位于玩家起始位置附近
        let firstPlatform = this.platforms[0];
        let firstPlatformData = this.platformsData[0];
        firstPlatform.x = -200; // 与玩家起始位置相近
        firstPlatform.y = 0;    // 确保高度合适
        lastRightEdge = firstPlatform.x + firstPlatformData.width / 2;
        
        // 循环放置剩余平台直到覆盖足够的距离
        for (let i = 1; i < this.platforms.length; i++) {
            let platform = this.platforms[i];
            let data = this.platformsData[i];
            
            // 计算平台的新位置，确保间距不会太大
            let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);
            let newX = lastRightEdge + gap + data.width / 2;
            
            // 设置初始位置
            platform.x = newX;
            
            // 如果有多个可能的高度，随机选择一个
            if (this.possibleHeights.length > 1) {
                let randomHeightIndex = Math.floor(Math.random() * this.possibleHeights.length);
                platform.y = this.possibleHeights[randomHeightIndex];
            }
            
            // 更新最右边的边缘位置
            lastRightEdge = newX + data.width / 2;
            
            // 如果已经覆盖了足够的距离，就结束
            if (lastRightEdge > targetRightEdge) {
                break;
            }
            
            // 如果所有平台都用完了但还没覆盖足够距离，就循环使用
            if (i === this.platforms.length - 1 && lastRightEdge < targetRightEdge) {
                i = 0; // 重置索引到1(下次循环会加1变成1)
                console.log("需要重用平台来覆盖更多区域");
            }
        }
        
        console.log("初始化平台完成，最远位置：" + lastRightEdge);
    },

    start() {
        // 确保所有地面都有物理碰撞器
        this.platforms.forEach(platform => {
            if (!platform.getComponent(cc.RigidBody)) {
                let rigidBody = platform.addComponent(cc.RigidBody);
                rigidBody.type = cc.RigidBodyType.Static;
                rigidBody.gravityScale = 0;
            }
            
            if (!platform.getComponent(cc.PhysicsBoxCollider)) {
                let collider = platform.addComponent(cc.PhysicsBoxCollider);
                collider.size = platform.getContentSize();
                collider.sensor = false;
                collider.friction = 0.2;
                collider.restitution = 0;
            }
        });
    },

    update(dt) {
        // 移动所有地面
        for (let i = 0; i < this.platforms.length; i++) {
            let platform = this.platforms[i];
            let data = this.platformsData[i];
            
            // 向左移动
            platform.x -= this.moveSpeed * dt;
            
            // 检查是否移出屏幕
            let rightEdgeOfPlatform = platform.x + data.width / 2;
            if (rightEdgeOfPlatform < -this.screenWidth / 2) {
                // 找到最右边的平台
                let rightmostX = -Infinity;
                this.platforms.forEach(p => {
                    let rightEdge = p.x + p.width / 2;
                    if (rightEdge > rightmostX) {
                        rightmostX = rightEdge;
                    }
                });
                
                // 随机选择一个高度
                let randomHeightIndex = Math.floor(Math.random() * this.possibleHeights.length);
                let newHeight = this.possibleHeights[randomHeightIndex];
                
                // 随机间距，但确保间距不会太大
                let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);
                
                // 重新放置在最右边的平台之后
                // 确保地面生成在足够远的地方，超过预生成距离
                let minRightPos = this.screenWidth / 2 + this.screenWidth * (this.preGenerateDistance - 1);
                rightmostX = Math.max(rightmostX, minRightPos);
                
                platform.x = rightmostX + gap + data.width / 2;
                platform.y = newHeight;
            }
        }
        
        // 检查是否需要提前生成更多地面
        this.checkAndEnsurePlatforms();
    },

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
    },

    // 重置所有平台位置
    resetPlatforms() {
        // 调用初始化位置函数
        this.initializePositions();
    },
    
    // 检查并确保有足够的地面
    checkAndEnsurePlatforms() {
        // 计算需要覆盖的最远距离
        let targetRightEdge = this.screenWidth / 2 + this.screenWidth * this.preGenerateDistance;
        
        // 找到最右边的平台
        let rightmostX = -Infinity;
        this.platforms.forEach(platform => {
            let rightEdge = platform.x + platform.width / 2;
            if (rightEdge > rightmostX) {
                rightmostX = rightEdge;
            }
        });
        
        // 如果最右边的平台不够远，立即生成更多地面
        if (rightmostX < targetRightEdge) {
            console.log("需要生成更多地面，当前最远：" + rightmostX + "，目标：" + targetRightEdge);
            
            // 找到可以重用的平台（已经移出屏幕左侧的平台）
            let availablePlatforms = this.platforms.filter(platform => {
                return platform.x + platform.width / 2 < -this.screenWidth / 2;
            });
            
            // 如果有可用平台，重新放置它们
            if (availablePlatforms.length > 0) {
                for (let i = 0; i < availablePlatforms.length; i++) {
                    let platform = availablePlatforms[i];
                    let data = this.platformsData[this.platforms.indexOf(platform)];
                    
                    // 随机选择一个高度
                    let randomHeightIndex = Math.floor(Math.random() * this.possibleHeights.length);
                    let newHeight = this.possibleHeights[randomHeightIndex];
                    
                    // 随机间距
                    let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);
                    
                    // 重新放置在最右边的平台之后
                    platform.x = rightmostX + gap + data.width / 2;
                    platform.y = newHeight;
                    
                    // 更新最右边的位置
                    rightmostX = platform.x + data.width / 2;
                    
                    // 如果已经覆盖了足够的距离，就结束
                    if (rightmostX >= targetRightEdge) {
                        break;
                    }
                }
            }
        }
    }
});
