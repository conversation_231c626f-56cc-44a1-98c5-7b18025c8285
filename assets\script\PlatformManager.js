cc.Class({
    extends: cc.Component,

    properties: {
        // ===== 基础配置 =====
        // 地面移动速度，将由GameManager控制
        moveSpeed: {
            default: 200,
            tooltip: "地面移动速度"
        },

        // 地面间距配置
        minGapX: {
            default: 50,
            tooltip: "地面之间的最小间距"
        },

        maxGapX: {
            default: 200,
            tooltip: "地面之间的最大间距"
        },

        // ===== 预制体引用 =====
        // 安全地面预制体
        safePlatformPrefab: {
            default: null,
            type: cc.Prefab,
            tooltip: "安全地面预制体，保证玩家可以安全跳跃"
        },

        // 挑战地面预制体数组
        challengePlatformPrefabs: {
            default: [],
            type: cc.Prefab,
            tooltip: "挑战地面预制体数组"
        },

        // ===== 生成规则配置 =====
        // 安全地面生成频率（每隔几个平台强制生成一个安全平台）
        safeFrequency: {
            default: 3,
            tooltip: "每隔几个平台强制生成一个安全平台"
        },

        // 预生成距离，屏幕宽度的倍数
        preGenerateDistance: {
            default: 2.5,
            tooltip: "提前多少屏幕宽度生成地面"
        }
    },

    onLoad() {
        console.log("PlatformManager 初始化开始");

        // 获取屏幕宽度
        this.screenWidth = cc.winSize.width;

        // 初始化状态标记
        this.isUsingPrefabs = false; // 是否已经切换到预制体生成模式
        this.platformCounter = 0;    // 生成的平台计数器，用于控制安全平台频率
        this.debugMoveCount = 0;     // 调试计数器，用于限制移动日志输出

        // 初始化数组
        this.presetPlatforms = [];   // 预设地面数组（场景中的Ground节点）
        this.prefabPlatforms = [];   // 预制体地面数组（动态生成的）
        this.platformPool = [];      // 地面对象池，用于回收重用

        // 获取预设地面（场景中的Ground节点）
        this.initPresetPlatforms();

        // 验证预制体引用
        this.validatePrefabs();

        console.log("PlatformManager 初始化完成");
        console.log("预设地面数量:", this.presetPlatforms.length);
        console.log("挑战地面预制体数量:", this.challengePlatformPrefabs.length);
    },

    // 获取场景中的预设地面
    initPresetPlatforms() {
        // 获取所有以"Ground"开头的子节点
        this.presetPlatforms = this.node.children.filter(child =>
            child.name.startsWith('Ground'));

        console.log("找到预设地面:", this.presetPlatforms.map(p => p.name));

        if (this.presetPlatforms.length === 0) {
            console.warn("警告：没有找到预设地面！请确保场景中有以'Ground'开头的节点。");
            return;
        }

        // 按名称排序，确保Ground1, Ground2, Ground3, Ground4的顺序
        this.presetPlatforms.sort((a, b) => {
            let aNum = parseInt(a.name.replace('Ground', '')) || 0;
            let bNum = parseInt(b.name.replace('Ground', '')) || 0;
            return aNum - bNum;
        });

        // 为每个预设地面添加标记和数据
        this.presetPlatforms.forEach((platform, index) => {
            platform._isPreset = true; // 标记为预设地面
            platform._isActive = true; // 标记为活跃状态
            platform._originalX = platform.x; // 记录原始X位置
            platform._originalY = platform.y; // 记录原始Y位置
            platform._width = platform.width; // 记录宽度

            console.log(`预设地面 ${platform.name}: 位置(${platform.x}, ${platform.y}), 宽度:${platform.width}`);
        });

        // 初始化预设地面的位置，确保它们形成合理的路径
        this.arrangePresetPlatforms();
    },

    // 重新排列预设地面，确保游戏开始时的路径合理
    arrangePresetPlatforms() {
        if (this.presetPlatforms.length === 0) return;

        // 玩家起始位置大约在 x = -200, y = -50
        let playerStartX = -200;
        let playerStartY = -50;

        // 第一个地面应该在玩家脚下或稍微靠前，确保安全
        let firstPlatform = this.presetPlatforms[0];
        firstPlatform.x = playerStartX + 100; // 稍微靠前一点
        firstPlatform.y = playerStartY - 80;  // 确保在玩家下方，给出安全距离

        console.log(`安全起始地面 ${firstPlatform.name}: 设置到位置(${firstPlatform.x}, ${firstPlatform.y})`);

        // 后续地面按照合理间距排列
        let lastRightEdge = firstPlatform.x + firstPlatform.width / 2;

        for (let i = 1; i < this.presetPlatforms.length; i++) {
            let platform = this.presetPlatforms[i];

            // 计算间距（在最小和最大间距之间）
            let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);

            // 设置X位置
            platform.x = lastRightEdge + gap + platform.width / 2;

            // Y位置可以有一些变化，但不要太极端
            let heightVariation = [-30, -10, 10, 30]; // 相对于起始高度的变化
            let randomHeight = firstPlatform.y + heightVariation[Math.floor(Math.random() * heightVariation.length)];
            platform.y = randomHeight;

            // 更新最右边缘
            lastRightEdge = platform.x + platform.width / 2;

            console.log(`预设地面 ${platform.name}: 重新排列到位置(${platform.x}, ${platform.y})`);
        }

        // 记录预设地面的最右边缘，用于后续预制体生成
        this.presetRightmostX = lastRightEdge;
        console.log("预设地面排列完成，最右边缘位置:", this.presetRightmostX);
    },

    // 验证预制体引用是否正确
    validatePrefabs() {
        let hasError = false;

        if (!this.safePlatformPrefab) {
            console.error("错误：未设置安全地面预制体！");
            hasError = true;
        }

        if (this.challengePlatformPrefabs.length === 0) {
            console.error("错误：未设置挑战地面预制体！");
            hasError = true;
        }

        if (hasError) {
            console.error("请在编辑器中为PlatformManager组件设置预制体引用！");
        } else {
            console.log("预制体验证通过");
        }
    },

    start() {
        // 确保所有预设地面都有物理组件
        this.setupPhysicsForPresetPlatforms();
    },

    // 为预设地面设置物理组件
    setupPhysicsForPresetPlatforms() {
        this.presetPlatforms.forEach(platform => {
            this.setupPlatformPhysics(platform);
        });
    },

    // 为单个地面设置物理组件
    setupPlatformPhysics(platform) {
        // 添加刚体组件
        if (!platform.getComponent(cc.RigidBody)) {
            let rigidBody = platform.addComponent(cc.RigidBody);
            rigidBody.type = cc.RigidBodyType.Static;
            rigidBody.gravityScale = 0;
        }

        // 添加碰撞器组件
        if (!platform.getComponent(cc.PhysicsBoxCollider)) {
            let collider = platform.addComponent(cc.PhysicsBoxCollider);
            collider.size = platform.getContentSize();
            collider.sensor = false;
            collider.friction = 0.2;
            collider.restitution = 0;
        }
    },

    update(dt) {
        // 移动所有活跃的地面
        this.moveAllPlatforms(dt);

        // 检查是否需要生成新地面
        this.checkAndGeneratePlatforms();

        // 回收移出屏幕的地面
        this.recyclePlatforms();
    },

    // 移动所有地面
    moveAllPlatforms(dt) {
        // 调试信息：只在前几次输出
        if (this.debugMoveCount < 3 && this.moveSpeed > 0) {
            console.log(`🚀 地面移动: 速度=${this.moveSpeed}, dt=${dt.toFixed(3)}, 移动距离=${(this.moveSpeed * dt).toFixed(2)}`);
            this.debugMoveCount++;
        }

        // 移动预设地面
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });

        // 移动预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                platform.x -= this.moveSpeed * dt;
            }
        });
    },

    // 检查并生成新地面
    checkAndGeneratePlatforms() {
        // 计算需要覆盖的目标距离（屏幕右边缘 + 预生成距离）
        let targetRightEdge = this.screenWidth / 2 + this.screenWidth * this.preGenerateDistance;

        // 获取当前最右边的地面位置
        let currentRightmostX = this.getRightmostPlatformX();

        // 检查是否需要切换到预制体生成模式
        if (!this.isUsingPrefabs) {
            // 检查是否应该开始使用预制体
            // 条件1：预设地面快用完了（最后一个预设地面已经在屏幕中央或更右）
            // 条件2：或者当前最右边的地面距离不够远

            let shouldSwitchToPrefabs = false;

            // 检查预设地面的使用情况
            if (this.presetPlatforms.length > 0) {
                let lastPresetPlatform = this.presetPlatforms[this.presetPlatforms.length - 1];
                let lastPresetRightEdge = lastPresetPlatform.x + lastPresetPlatform.width / 2;

                // 如果最后一个预设地面已经接近屏幕中央，就开始切换
                if (lastPresetRightEdge < this.screenWidth / 4) {
                    shouldSwitchToPrefabs = true;
                    console.log("预设地面即将用完，准备切换到预制体生成模式");
                }
            }

            // 或者如果当前最右边的地面距离不够远
            if (currentRightmostX < targetRightEdge) {
                shouldSwitchToPrefabs = true;
                console.log("当前地面距离不够，需要生成更多地面");
            }

            if (shouldSwitchToPrefabs) {
                this.isUsingPrefabs = true;
                console.log("🔄 切换到预制体生成模式");
            }
        }

        // 如果已经在使用预制体模式，检查是否需要生成新地面
        if (this.isUsingPrefabs && currentRightmostX < targetRightEdge) {
            this.generatePrefabPlatform();
        }
    },

    // 生成预制体地面
    generatePrefabPlatform() {
        // 验证预制体引用
        if (!this.safePlatformPrefab || this.challengePlatformPrefabs.length === 0) {
            console.error("无法生成预制体地面：预制体引用未设置");
            return;
        }

        // 获取当前最右边的地面位置
        let currentRightmostX = this.getRightmostPlatformX();

        // 决定生成安全地面还是挑战地面
        let shouldGenerateSafe = this.shouldGenerateSafePlatform();
        let prefabToUse = shouldGenerateSafe ? this.safePlatformPrefab : this.getRandomChallengePrefab();

        // 尝试从对象池获取，如果没有则实例化新的
        let newPlatform = this.getPlatformFromPool(prefabToUse);
        if (!newPlatform) {
            newPlatform = cc.instantiate(prefabToUse);
            console.log("创建新的预制体地面实例");
        } else {
            console.log("从对象池重用地面");
        }

        // 设置父节点
        newPlatform.parent = this.node;

        // 计算位置
        let gap = this.minGapX + Math.random() * (this.maxGapX - this.minGapX);
        let newX = currentRightmostX + gap + newPlatform.width / 2;
        let newY = this.calculatePlatformY(shouldGenerateSafe);

        // 设置位置
        newPlatform.x = newX;
        newPlatform.y = newY;

        // 添加标记
        newPlatform._isPreset = false;
        newPlatform._isActive = true;
        newPlatform._isSafe = shouldGenerateSafe;

        // 设置物理组件
        this.setupPlatformPhysics(newPlatform);

        // 添加到预制体地面数组
        this.prefabPlatforms.push(newPlatform);

        // 更新计数器
        this.platformCounter++;

        let platformType = shouldGenerateSafe ? "安全" : "挑战";
        console.log(`✨ 生成${platformType}地面: 位置(${newX.toFixed(1)}, ${newY.toFixed(1)}), 计数器:${this.platformCounter}`);
    },

    // 判断是否应该生成安全地面
    shouldGenerateSafePlatform() {
        // 每隔 safeFrequency 个平台强制生成一个安全平台
        return (this.platformCounter % this.safeFrequency === 0);
    },

    // 获取随机的挑战地面预制体
    getRandomChallengePrefab() {
        let randomIndex = Math.floor(Math.random() * this.challengePlatformPrefabs.length);
        return this.challengePlatformPrefabs[randomIndex];
    },

    // 计算地面的Y位置
    calculatePlatformY(isSafe) {
        if (isSafe) {
            // 安全地面：高度变化较小，确保玩家容易跳到
            let safeHeights = [-130, -110, -90]; // 相对安全的高度
            return safeHeights[Math.floor(Math.random() * safeHeights.length)];
        } else {
            // 挑战地面：可以有更大的高度变化
            let challengeHeights = [-150, -130, -110, -90, -70, -50]; // 更多样的高度
            return challengeHeights[Math.floor(Math.random() * challengeHeights.length)];
        }
    },

    // 从对象池获取地面
    getPlatformFromPool(prefab) {
        // 简单实现：从池中取第一个可用的
        // 更复杂的实现可以根据预制体类型匹配
        if (this.platformPool.length > 0) {
            let platform = this.platformPool.pop();
            platform.active = true; // 重新激活
            return platform;
        }
        return null;
    },

    // 回收移出屏幕的地面
    recyclePlatforms() {
        let leftBoundary = -this.screenWidth / 2 - 100; // 屏幕左边界外100像素

        // 检查预设地面
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                // 预设地面移出屏幕后，标记为非活跃，不再重用
                platform._isActive = false;
                console.log(`预设地面 ${platform.name} 移出屏幕，标记为非活跃`);
            }
        });

        // 检查预制体地面的回收
        for (let i = this.prefabPlatforms.length - 1; i >= 0; i--) {
            let platform = this.prefabPlatforms[i];
            if (platform._isActive && platform.x + platform.width / 2 < leftBoundary) {
                // 预制体地面移出屏幕后，回收到对象池
                platform._isActive = false;
                platform.active = false; // 隐藏节点

                // 移到对象池
                this.platformPool.push(platform);
                this.prefabPlatforms.splice(i, 1);

                console.log(`预制体地面回收到对象池，当前池大小:${this.platformPool.length}`);
            }
        }
    },

    // 获取当前最右边的活跃地面位置
    getRightmostPlatformX() {
        let rightmostX = -Infinity;

        // 检查预设地面
        this.presetPlatforms.forEach(platform => {
            if (platform._isActive) {
                let rightEdge = platform.x + platform.width / 2;
                if (rightEdge > rightmostX) {
                    rightmostX = rightEdge;
                }
            }
        });

        // 检查预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform._isActive) {
                let rightEdge = platform.x + platform.width / 2;
                if (rightEdge > rightmostX) {
                    rightmostX = rightEdge;
                }
            }
        });

        return rightmostX;
    },

    // 检查是否还有活跃的预设地面
    hasActivePresetPlatforms() {
        return this.presetPlatforms.some(platform => platform._isActive);
    },

    // ===== 公共接口方法 =====

    // 设置移动速度，由GameManager调用
    setMoveSpeed(speed) {
        this.moveSpeed = speed;
        console.log(`🏃 PlatformManager移动速度设置为: ${speed}`);
    },

    // 重置所有平台位置
    resetPlatforms() {
        console.log("重置所有平台位置");

        // 重置状态标记
        this.isUsingPrefabs = false;
        this.platformCounter = 0;

        // 重置预设地面
        this.presetPlatforms.forEach(platform => {
            platform._isActive = true;
            // 可以选择恢复原始位置，或者重新排列
            // platform.x = platform._originalX;
            // platform.y = platform._originalY;
        });

        // 重新排列预设地面
        this.arrangePresetPlatforms();

        // 清理所有预制体地面
        this.prefabPlatforms.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.prefabPlatforms = [];

        // 清理对象池
        this.platformPool.forEach(platform => {
            if (platform && platform.isValid) {
                platform.destroy();
            }
        });
        this.platformPool = [];

        console.log("平台重置完成");
    }
});