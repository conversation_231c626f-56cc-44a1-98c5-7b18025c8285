cc.Class({
    extends: cc.Component,

    properties: {
        // 玩家节点
        player: {
            default: null,
            type: cc.Node
        },
        
        // 平台管理器
        platformManager: {
            default: null,
            type: cc.Node
        },

        // 金币管理器
        coinManager: {
            default: null,
            type: cc.Node
        },

        // UI管理器（可选，用于显示分数和游戏状态）
        scoreLabel: {
            default: null,
            type: cc.Label
        },
        
        // 初始移动速度
        startSpeed: 200,
        
        // 速度增加率（每秒）
        speedIncreaseRate: 10,
        
        // 最大速度
        maxSpeed: 500
    },

    onLoad() {
        // 初始化物理引擎
        let physicsManager = cc.director.getPhysicsManager();
        physicsManager.enabled = true;
        physicsManager.gravity = cc.v2(0, -800); // 设置重力
        
        // 初始化游戏状态
        this.isGameOver = false;
        this.isPaused = false;
        
        // 初始化分数
        this.score = 0;
        this.distance = 0;
        
        // 获取平台管理器组件
        this.platformManagerComp = this.platformManager.getComponent('PlatformManager');

        // 获取金币管理器组件
        if (this.coinManager) {
            this.coinManagerComp = this.coinManager.getComponent('CoinMgr');
        }

        // 获取玩家组件
        if (this.player) {
            this.playerComp = this.player.getComponent('player1');
        }
        
        // 设置输入事件
        this.setupInputEvents();
    },

    start() {
        // 设置初始速度
        this.currentSpeed = this.startSpeed;
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
        }
        
        // 更新分数显示
        this.updateScoreDisplay();
    },

    update(dt) {
        if (this.isGameOver || this.isPaused) return;
        
        // 增加距离和分数
        this.distance += this.currentSpeed * dt;
        this.score = Math.floor(this.distance / 10);
        
        // 逐渐增加速度
        this.currentSpeed = Math.min(this.maxSpeed, this.currentSpeed + this.speedIncreaseRate * dt);
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        }
        if (this.coinManagerComp) {
            this.coinManagerComp.setMoveSpeed(this.currentSpeed);
        }
        
        // 更新分数显示
        this.updateScoreDisplay();
        
        // 检查游戏是否结束
        this.checkGameOver();
    },
    
    updateScoreDisplay() {
        if (this.scoreLabel) {
            this.scoreLabel.string = "分数: " + this.score;
        }
    },
    
    // 增加分数方法（供金币碰撞回调使用）
    addScore(points) {
        this.score += points;
        console.log("增加分数:", points, "当前分数:", this.score);
        this.updateScoreDisplay();
    },
    
    checkGameOver() {
        // 如果玩家组件报告游戏结束
        if (this.playerComp && this.playerComp.isGameOver) {
            this.gameOver();
        }
        
        // 或者检查玩家是否掉出了屏幕
        if (this.player && this.player.y < -cc.winSize.height / 2 - 100) {
            this.gameOver();
        }
    },
    
    gameOver() {
        if (this.isGameOver) return; // 防止多次调用
        
        this.isGameOver = true;
        console.log("游戏结束! 最终分数:", this.score);
        
        // 停止平台移动
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(0);
        }
        
        // 这里可以添加游戏结束UI的显示
        // ...
        
        // 延迟几秒后显示重新开始按钮
        this.scheduleOnce(() => {
            // 显示重新开始按钮
            // ...
        }, 2);
    },
    
    restartGame() {
        // 重置游戏状态
        this.isGameOver = false;
        this.score = 0;
        this.distance = 0;
        this.currentSpeed = this.startSpeed;
        
        // 重置平台位置
        if (this.platformManagerComp) {
            this.platformManagerComp.resetPlatforms();
            this.platformManagerComp.setMoveSpeed(this.startSpeed);
        }

        // 重置金币管理器
        if (this.coinManagerComp) {
            this.coinManagerComp.resetCoins();
            this.coinManagerComp.setMoveSpeed(this.startSpeed);
        }

        // 重置玩家位置和状态
        if (this.playerComp) {
            this.playerComp.reset();
        }
        
        // 更新分数显示
        this.updateScoreDisplay();
        
        // 隐藏游戏结束UI
        // ...
    },
    
    pauseGame() {
        this.isPaused = true;
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(0);
        }
    },
    
    resumeGame() {
        this.isPaused = false;
        if (this.platformManagerComp) {
            this.platformManagerComp.setMoveSpeed(this.currentSpeed);
        }
    },
    
    setupInputEvents() {
        // 设置键盘事件
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        
        // 可以添加触摸事件用于移动设备
        // this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
    },
    
    onKeyDown(event) {
        switch(event.keyCode) {
            case cc.macro.KEY.p:
                // P键暂停/恢复游戏
                if (this.isPaused) {
                    this.resumeGame();
                } else {
                    this.pauseGame();
                }
                break;
            case cc.macro.KEY.r:
                // R键重新开始游戏
                if (this.isGameOver) {
                    this.restartGame();
                }
                break;
        }
    },
    
    onDestroy() {
        // 移除事件监听
        cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    }
});
